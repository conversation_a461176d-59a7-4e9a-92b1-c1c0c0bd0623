#include "ui/single_stream_main_window.hpp"
#include "widgets/single_stream_display.hpp"
#include "widgets/face_recognition_sidebar.hpp"
#include <QApplication>
#include <QMenuBar>
#include <QToolBar>
#include <QStatusBar>
#include <QAction>
#include <QIcon>
#include <QMessageBox>
#include <QSettings>
#include <QSplitter>
#include <QHBoxLayout>
#include <QLabel>
#include <QTimer>
#include <QDateTime>
#include <QDebug>
#include <QRandomGenerator>

SingleStreamMainWindow::SingleStreamMainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_centralWidget(nullptr)
    , m_mainLayout(nullptr)
    , m_mainSplitter(nullptr)
    , m_streamDisplay(nullptr)
    , m_faceRecognitionSidebar(nullptr)
    , m_fullscreenAction(nullptr)
    , m_settingsAction(nullptr)
    , m_aboutAction(nullptr)
    , m_exitAction(nullptr)
    , m_statusLabel(nullptr)
    , m_streamStatusLabel(nullptr)
    , m_faceCountLabel(nullptr)
    , m_mockDataTimer(nullptr)
    , m_settings(nullptr)
    , m_isFullscreen(false)
{
    setWindowTitle("C-AIBOX - Face Recognition System");
    setMinimumSize(1200, 800);
    resize(1600, 900);

    // Initialize settings
    m_settings = new QSettings(this);

    setupUI();
    setupMenuBar();
    setupToolBar();
    setupStatusBar();
    loadStyleSheet();
    connectSignals();
    startMockDataTimer();

    // Restore window state
    restoreGeometry(m_settings->value("geometry").toByteArray());
    restoreState(m_settings->value("windowState").toByteArray());
}

SingleStreamMainWindow::~SingleStreamMainWindow()
{
    // Save window state
    if (m_settings) {
        m_settings->setValue("geometry", saveGeometry());
        m_settings->setValue("windowState", saveState());
    }

    if (m_mockDataTimer) {
        m_mockDataTimer->stop();
    }
}

void SingleStreamMainWindow::setupUI()
{
    // Create central widget
    m_centralWidget = new QWidget(this);
    setCentralWidget(m_centralWidget);

    // Create main layout
    m_mainLayout = new QHBoxLayout(m_centralWidget);
    m_mainLayout->setContentsMargins(0, 0, 0, 0);
    m_mainLayout->setSpacing(0);

    // Create main splitter
    m_mainSplitter = new QSplitter(Qt::Horizontal, m_centralWidget);
    m_mainLayout->addWidget(m_mainSplitter);

    // Create stream display
    m_streamDisplay = new SingleStreamDisplay();
    m_streamDisplay->setStreamTitle("Camera 1 - 192.168.1.101");
    m_streamDisplay->setStreamUrl("rtsp://192.168.1.101:554/stream1");
    
    // Create face recognition sidebar
    m_faceRecognitionSidebar = new FaceRecognitionSidebar();

    // Add widgets to splitter
    m_mainSplitter->addWidget(m_streamDisplay);
    m_mainSplitter->addWidget(m_faceRecognitionSidebar);

    // Set splitter proportions (stream takes most space, sidebar is fixed width)
    m_mainSplitter->setSizes({1000, 350});
    m_mainSplitter->setStretchFactor(0, 1); // Stream display stretches
    m_mainSplitter->setStretchFactor(1, 0); // Sidebar fixed width
}

void SingleStreamMainWindow::setupMenuBar()
{
    // File menu
    QMenu* fileMenu = menuBar()->addMenu("&File");
    
    m_settingsAction = new QAction("&Settings", this);
    m_settingsAction->setShortcut(QKeySequence::Preferences);
    m_settingsAction->setStatusTip("Open application settings");
    fileMenu->addAction(m_settingsAction);
    
    fileMenu->addSeparator();
    
    m_exitAction = new QAction("E&xit", this);
    m_exitAction->setShortcut(QKeySequence::Quit);
    m_exitAction->setStatusTip("Exit the application");
    fileMenu->addAction(m_exitAction);

    // View menu
    QMenu* viewMenu = menuBar()->addMenu("&View");
    
    m_fullscreenAction = new QAction("&Fullscreen", this);
    m_fullscreenAction->setShortcut(QKeySequence::FullScreen);
    m_fullscreenAction->setStatusTip("Toggle fullscreen mode");
    m_fullscreenAction->setCheckable(true);
    viewMenu->addAction(m_fullscreenAction);

    // Help menu
    QMenu* helpMenu = menuBar()->addMenu("&Help");
    
    m_aboutAction = new QAction("&About", this);
    m_aboutAction->setStatusTip("Show application information");
    helpMenu->addAction(m_aboutAction);
}

void SingleStreamMainWindow::setupToolBar()
{
    QToolBar* mainToolBar = addToolBar("Main");
    mainToolBar->setToolButtonStyle(Qt::ToolButtonTextUnderIcon);
    
    // Add main actions to toolbar
    mainToolBar->addAction(m_fullscreenAction);
    mainToolBar->addSeparator();
    mainToolBar->addAction(m_settingsAction);
}

void SingleStreamMainWindow::setupStatusBar()
{
    // Create status bar labels
    m_statusLabel = new QLabel("Ready");
    statusBar()->addWidget(m_statusLabel);
    
    statusBar()->addPermanentWidget(new QLabel(" | "));
    
    m_streamStatusLabel = new QLabel("Stream: Active");
    statusBar()->addPermanentWidget(m_streamStatusLabel);
    
    statusBar()->addPermanentWidget(new QLabel(" | "));
    
    m_faceCountLabel = new QLabel("Faces: 0");
    statusBar()->addPermanentWidget(m_faceCountLabel);
    
    // Update status periodically
    QTimer* statusTimer = new QTimer(this);
    connect(statusTimer, &QTimer::timeout, this, [this]() {
        QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
        m_statusLabel->setText(QString("Last update: %1").arg(timestamp));
    });
    statusTimer->start(1000); // Update every second
}

void SingleStreamMainWindow::loadStyleSheet()
{
    setStyleSheet(R"(
        QMainWindow {
            background-color: #2b2b2b;
        }
        QMenuBar {
            background-color: #3c3c3c;
            color: white;
            border-bottom: 1px solid #555;
        }
        QMenuBar::item {
            background-color: transparent;
            padding: 4px 8px;
        }
        QMenuBar::item:selected {
            background-color: #555;
        }
        QMenu {
            background-color: #3c3c3c;
            color: white;
            border: 1px solid #555;
        }
        QMenu::item:selected {
            background-color: #555;
        }
        QToolBar {
            background-color: #3c3c3c;
            border: none;
            spacing: 3px;
        }
        QStatusBar {
            background-color: #3c3c3c;
            color: white;
            border-top: 1px solid #555;
        }
        QStatusBar QLabel {
            color: white;
        }
    )");
}

void SingleStreamMainWindow::connectSignals()
{
    // Menu actions
    connect(m_exitAction, &QAction::triggered, this, &QWidget::close);
    connect(m_fullscreenAction, &QAction::triggered, this, &SingleStreamMainWindow::onToggleFullscreen);
    connect(m_settingsAction, &QAction::triggered, this, &SingleStreamMainWindow::onShowSettings);
    connect(m_aboutAction, &QAction::triggered, this, &SingleStreamMainWindow::onAbout);

    // Note: Stream display and sidebar signals removed since we removed Q_OBJECT
    // Direct function calls can be used instead if needed
}

void SingleStreamMainWindow::startMockDataTimer()
{
    // Timer to add mock face detection results periodically
    m_mockDataTimer = new QTimer(this);
    connect(m_mockDataTimer, &QTimer::timeout, [this]() { onUpdateMockData(); });
    m_mockDataTimer->start(5000); // Add new face result every 5 seconds
}

void SingleStreamMainWindow::onStreamClicked()
{
    qDebug() << "Stream clicked";
}

void SingleStreamMainWindow::onStreamDoubleClicked()
{
    // Toggle fullscreen on double-click
    onToggleFullscreen();
}

void SingleStreamMainWindow::onFaceResultClicked(const FaceResult& result)
{
    qDebug() << "Face result clicked:" << result.name << result.confidence;
    m_statusLabel->setText(QString("Selected: %1 (%2)").arg(result.name).arg(result.confidence));
}

void SingleStreamMainWindow::onToggleFullscreen()
{
    if (m_isFullscreen) {
        showNormal();
        m_isFullscreen = false;
        m_fullscreenAction->setChecked(false);
    } else {
        showFullScreen();
        m_isFullscreen = true;
        m_fullscreenAction->setChecked(true);
    }
}

void SingleStreamMainWindow::onShowSettings()
{
    // TODO: Implement settings dialog
    QMessageBox::information(this, "Settings", "Settings dialog not yet implemented.");
}

void SingleStreamMainWindow::onAbout()
{
    QMessageBox::about(this, "About C-AIBOX", 
        "C-AIBOX Face Recognition System\n\n"
        "Version 1.0.0\n"
        "Built with Qt and optimized for RK3588\n\n"
        "© 2024 C-AIBOX Team");
}

void SingleStreamMainWindow::onUpdateMockData()
{
    addMockFaceResults();
}

void SingleStreamMainWindow::addMockFaceResults()
{
    // Add random mock face results to demonstrate the sidebar
    QStringList names = {
        "Nguyễn Thái Quốc Huy", 
        "Trịnh Quốc Bảo", 
        "Trần Thành Long", 
        "Lê Văn Nam",
        "Phạm Thị Mai",
        "Unknown Person"
    };
    
    QStringList statuses = {"Cho phép", "Từ chối"};
    
    // Randomly add 0-2 new face results
    int count = QRandomGenerator::global()->bounded(0, 3);
    for (int i = 0; i < count; ++i) {
        QString name = names[QRandomGenerator::global()->bounded(names.size())];
        QString confidence = QString("%1%").arg(QRandomGenerator::global()->bounded(75, 99));
        QString status = (name == "Unknown Person") ? "Từ chối" : 
                        statuses[QRandomGenerator::global()->bounded(statuses.size())];
        
        FaceResult result(name, confidence, status);
        m_faceRecognitionSidebar->addFaceResult(result);
    }
    
    // Update face count in status bar
    // Note: This is a simplified count, in real implementation you'd get this from the sidebar
    static int totalFaces = 0;
    totalFaces += count;
    m_faceCountLabel->setText(QString("Faces: %1").arg(totalFaces));
}
